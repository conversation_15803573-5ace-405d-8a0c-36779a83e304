import sys
import os
import cv2
import numpy as np
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtCore import pyqtSignal, QThread

# 新增: 尝试导入 pyserial（若未安装，会在运行时报缺失）
try:
    import serial
except Exception:
    serial = None


class FrameGrabber(QThread):
    frame_ready = pyqtSignal(np.ndarray)
    error = pyqtSignal(str)

    def __init__(self, source=0, serial_mode=False, baud=115200, parent=None):
        super().__init__(parent)
        self.source = source
        self.serial_mode = serial_mode
        self.baud = baud
        self._running = False
        self.cap = None
        self.ser = None

    def run(self):
        if self.serial_mode:
            if serial is None:
                self.error.emit('未安装 pyserial（pip install pyserial）')
                return
            try:
                # source should be like 'COM1' or '/dev/ttyUSB0'
                self.ser = serial.Serial(self.source, self.baud, timeout=1)
            except Exception as e:
                self.error.emit(f'无法打开串口 {self.source}: {e}')
                return

            self._running = True
            buf = bytearray()
            while self._running:
                try:
                    data = self.ser.read(4096)
                    if not data:
                        self.msleep(10)
                        continue
                    buf.extend(data)
                    # 寻找 JPEG SOI(FFD8) 和 EOI(FFD9)
                    while True:
                        soi = buf.find(b'\xff\xd8')
                        if soi == -1:
                            # 没有起始，保留尾部少量以防分段
                            if len(buf) > 4096:
                                buf = buf[-1024:]
                            break
                        eoi = buf.find(b'\xff\xd9', soi+2)
                        if eoi == -1:
                            # 等待后续数据
                            if soi > 0:
                                # 丢弃前导垃圾
                                buf = buf[soi:]
                            break
                        jpg = bytes(buf[soi:eoi+2])
                        # 移除已消费部分
                        buf = buf[eoi+2:]
                        # 解码 JPEG
                        arr = np.frombuffer(jpg, dtype=np.uint8)
                        frame = cv2.imdecode(arr, cv2.IMREAD_COLOR)
                        if frame is None:
                            continue
                        self.frame_ready.emit(frame)
                except Exception as e:
                    self.error.emit('串口读取错误: ' + str(e))
                    break

            try:
                if self.ser is not None:
                    self.ser.close()
            except Exception:
                pass
            return

        # 非串口模式，使用 OpenCV VideoCapture
        try:
            self.cap = cv2.VideoCapture(self.source)
            if not self.cap.isOpened():
                self.error.emit(f'无法打开视频源: {self.source}')
                return
        except Exception as e:
            self.error.emit(str(e))
            return

        self._running = True
        while self._running:
            ret, frame = self.cap.read()
            if not ret or frame is None:
                self.msleep(30)
                continue
            self.frame_ready.emit(frame)

        try:
            if self.cap is not None:
                self.cap.release()
        except Exception:
            pass

    def stop(self):
        self._running = False
        self.wait(1000)


class MainWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('MaixCAM Prox - PyQt5 Viewer')
        self.resize(960, 720)

        self.image_label = QtWidgets.QLabel('未连接')
        self.image_label.setAlignment(QtCore.Qt.AlignCenter)
        self.image_label.setStyleSheet('background: #222; color: #eee;')

        self.src_input = QtWidgets.QLineEdit('COM1')
        self.src_input.setToolTip('摄像头设备索引(例如0)、串口名(例如 COM1) 或 流URL')

        # 新增：是否串口模式与波特率输入
        self.serial_checkbox = QtWidgets.QCheckBox('串口模式')
        self.baud_input = QtWidgets.QLineEdit('115200')
        self.baud_input.setMaximumWidth(120)
        self.baud_input.setToolTip('串口波特率，例如 115200')

        self.start_btn = QtWidgets.QPushButton('开始')
        self.stop_btn = QtWidgets.QPushButton('停止')
        self.snap_btn = QtWidgets.QPushButton('抓图')
        self.status_label = QtWidgets.QLabel('状态: 就绪')

        hl = QtWidgets.QHBoxLayout()
        hl.addWidget(QtWidgets.QLabel('源:'))
        hl.addWidget(self.src_input)
        hl.addWidget(self.serial_checkbox)
        hl.addWidget(QtWidgets.QLabel('波特率:'))
        hl.addWidget(self.baud_input)
        hl.addWidget(self.start_btn)
        hl.addWidget(self.stop_btn)
        hl.addWidget(self.snap_btn)

        v = QtWidgets.QVBoxLayout(self)
        v.addWidget(self.image_label, stretch=1)
        v.addLayout(hl)
        v.addWidget(self.status_label)

        self.grabber = None
        self.current_frame = None

        self.start_btn.clicked.connect(self.start_capture)
        self.stop_btn.clicked.connect(self.stop_capture)
        self.snap_btn.clicked.connect(self.snapshot)

    def start_capture(self):
        if self.grabber is not None and self.grabber.isRunning():
            self.status_label.setText('状态: 已在运行')
            return

        src_text = self.src_input.text().strip()
        if src_text == '':
            self.status_label.setText('状态: 请输入摄像头索引/串口名/流URL')
            return

        serial_mode = self.serial_checkbox.isChecked()
        # 如果用户没有打勾但输入像 COM 开头，则也尝试串口模式
        if not serial_mode and (src_text.upper().startswith('COM') or src_text.startswith('/dev')):
            serial_mode = True

        # 解析波特率
        try:
            baud = int(self.baud_input.text().strip()) if serial_mode else 115200
        except Exception:
            baud = 115200

        # decide int or string for non-serial
        if serial_mode:
            source = src_text
        else:
            try:
                source = int(src_text)
            except Exception:
                source = src_text

        self.grabber = FrameGrabber(source, serial_mode=serial_mode, baud=baud)
        self.grabber.frame_ready.connect(self.on_frame)
        self.grabber.error.connect(self.on_error)
        self.grabber.start()
        self.status_label.setText(f'状态: 连接到 {source}，串口模式={serial_mode}，等待帧...')

    def stop_capture(self):
        if self.grabber is None:
            self.status_label.setText('状态: 未运行')
            return
        self.grabber.stop()
        self.grabber = None
        self.status_label.setText('状态: 已停止')
        self.image_label.setText('未连接')
        self.current_frame = None

    def on_error(self, msg):
        self.status_label.setText('错误: ' + msg)

    def on_frame(self, frame: np.ndarray):
        # keep last raw frame
        self.current_frame = frame
        # convert BGR -> RGB
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb.shape
        bytes_per_line = ch * w
        qt_image = QtGui.QImage(rgb.data, w, h, bytes_per_line, QtGui.QImage.Format_RGB888)
        pix = QtGui.QPixmap.fromImage(qt_image).scaled(self.image_label.width(), self.image_label.height(), QtCore.Qt.KeepAspectRatio)
        self.image_label.setPixmap(pix)
        self.status_label.setText('状态: 运行中')

    def snapshot(self):
        if self.current_frame is None:
            self.status_label.setText('状态: 无可保存帧')
            return
        # ensure snapshots dir
        out_dir = os.path.join(os.path.dirname(__file__), 'snapshots')
        os.makedirs(out_dir, exist_ok=True)
        fname = os.path.join(out_dir, f'snap_{QtCore.QDateTime.currentDateTime().toString("yyyyMMdd_HHmmss")}.png')
        cv2.imwrite(fname, self.current_frame)
        self.status_label.setText('已保存: ' + fname)


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    w = MainWindow()
    w.show()
    sys.exit(app.exec_())
