import sys
import os
import cv2
import numpy as np
import time
import socket
import requests
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtCore import pyqtSignal, QThread, QTimer
from urllib.parse import urlparse

# 新增: 尝试导入 pyserial（若未安装，会在运行时报缺失）
try:
    import serial
    import serial.tools.list_ports
except Exception:
    serial = None


class FrameGrabber(QThread):
    frame_ready = pyqtSignal(np.ndarray)
    error = pyqtSignal(str)

    def __init__(self, source=0, connection_mode='serial', baud=115200, parent=None):
        super().__init__(parent)
        self.source = source
        self.connection_mode = connection_mode  # 'serial', 'http', 'rtsp', 'tcp'
        self.baud = baud
        self._running = False
        self.cap = None
        self.ser = None
        self.sock = None

    def run(self):
        if self.connection_mode == 'serial':
            self._run_serial()
        elif self.connection_mode == 'http':
            self._run_http()
        elif self.connection_mode == 'rtsp':
            self._run_rtsp()
        elif self.connection_mode == 'tcp':
            self._run_tcp()
        else:
            self._run_camera()

    def _run_serial(self):
        """串口连接模式"""
        if serial is None:
            self.error.emit('未安装 pyserial（pip install pyserial）')
            return
        try:
            # source should be like 'COM1' or '/dev/ttyUSB0'
            self.ser = serial.Serial(self.source, self.baud, timeout=1)
        except Exception as e:
            self.error.emit(f'无法打开串口 {self.source}: {e}')
            return

        self._running = True
        buf = bytearray()
        while self._running:
            try:
                data = self.ser.read(4096)
                if not data:
                    self.msleep(10)
                    continue
                buf.extend(data)
                # 寻找 JPEG SOI(FFD8) 和 EOI(FFD9)
                while True:
                    soi = buf.find(b'\xff\xd8')
                    if soi == -1:
                        # 没有起始，保留尾部少量以防分段
                        if len(buf) > 4096:
                            buf = buf[-1024:]
                        break
                    eoi = buf.find(b'\xff\xd9', soi+2)
                    if eoi == -1:
                        # 等待后续数据
                        if soi > 0:
                            # 丢弃前导垃圾
                            buf = buf[soi:]
                        break
                    jpg = bytes(buf[soi:eoi+2])
                    # 移除已消费部分
                    buf = buf[eoi+2:]
                    # 解码 JPEG
                    arr = np.frombuffer(jpg, dtype=np.uint8)
                    frame = cv2.imdecode(arr, cv2.IMREAD_COLOR)
                    if frame is None:
                        continue
                    self.frame_ready.emit(frame)
            except Exception as e:
                self.error.emit('串口读取错误: ' + str(e))
                break

        try:
            if self.ser is not None:
                self.ser.close()
        except Exception:
            pass

    def _run_http(self):
        """HTTP连接模式 - 从MaixCAM Pro的HTTP服务获取图像"""
        self._running = True
        session = requests.Session()
        session.timeout = 5

        while self._running:
            try:
                # 尝试从HTTP端点获取图像
                response = session.get(f"http://{self.source}/capture", timeout=3)
                if response.status_code == 200:
                    # 解码图像
                    arr = np.frombuffer(response.content, dtype=np.uint8)
                    frame = cv2.imdecode(arr, cv2.IMREAD_COLOR)
                    if frame is not None:
                        self.frame_ready.emit(frame)
                else:
                    self.msleep(100)
            except Exception as e:
                self.error.emit(f'HTTP连接错误: {str(e)}')
                break
            self.msleep(33)  # ~30 FPS

    def _run_tcp(self):
        """TCP Socket连接模式"""
        try:
            host, port = self.source.split(':')
            port = int(port)
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(5)
            self.sock.connect((host, port))
        except Exception as e:
            self.error.emit(f'TCP连接失败 {self.source}: {e}')
            return

        self._running = True
        buf = bytearray()

        while self._running:
            try:
                data = self.sock.recv(4096)
                if not data:
                    self.msleep(10)
                    continue
                buf.extend(data)

                # 处理JPEG数据流（与串口模式相同）
                while True:
                    soi = buf.find(b'\xff\xd8')
                    if soi == -1:
                        if len(buf) > 4096:
                            buf = buf[-1024:]
                        break
                    eoi = buf.find(b'\xff\xd9', soi+2)
                    if eoi == -1:
                        if soi > 0:
                            buf = buf[soi:]
                        break
                    jpg = bytes(buf[soi:eoi+2])
                    buf = buf[eoi+2:]

                    arr = np.frombuffer(jpg, dtype=np.uint8)
                    frame = cv2.imdecode(arr, cv2.IMREAD_COLOR)
                    if frame is not None:
                        self.frame_ready.emit(frame)
            except Exception as e:
                self.error.emit(f'TCP读取错误: {str(e)}')
                break

        try:
            if self.sock:
                self.sock.close()
        except Exception:
            pass

    def _run_rtsp(self):
        """RTSP流连接模式"""
        try:
            rtsp_url = f"rtsp://{self.source}/stream"
            self.cap = cv2.VideoCapture(rtsp_url)
            if not self.cap.isOpened():
                self.error.emit(f'无法打开RTSP流: {rtsp_url}')
                return
        except Exception as e:
            self.error.emit(f'RTSP连接错误: {str(e)}')
            return

        self._run_camera_loop()

    def _run_camera(self):
        """摄像头模式"""
        try:
            self.cap = cv2.VideoCapture(self.source)
            if not self.cap.isOpened():
                self.error.emit(f'无法打开视频源: {self.source}')
                return
        except Exception as e:
            self.error.emit(str(e))
            return

        self._run_camera_loop()

    def _run_camera_loop(self):
        """摄像头/RTSP的通用循环"""
        self._running = True
        while self._running:
            ret, frame = self.cap.read()
            if not ret or frame is None:
                self.msleep(30)
                continue
            self.frame_ready.emit(frame)

        try:
            if self.cap is not None:
                self.cap.release()
        except Exception:
            pass

        # 非串口模式，使用 OpenCV VideoCapture
        try:
            self.cap = cv2.VideoCapture(self.source)
            if not self.cap.isOpened():
                self.error.emit(f'无法打开视频源: {self.source}')
                return
        except Exception as e:
            self.error.emit(str(e))
            return

        self._running = True
        while self._running:
            ret, frame = self.cap.read()
            if not ret or frame is None:
                self.msleep(30)
                continue
            self.frame_ready.emit(frame)

        try:
            if self.cap is not None:
                self.cap.release()
        except Exception:
            pass

    def stop(self):
        self._running = False
        self.wait(1000)


class MainWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('MaixCAM Pro - PyQt5 实时识别查看器')
        self.resize(1200, 800)

        self.image_label = QtWidgets.QLabel('未连接 MaixCAM Pro')
        self.image_label.setAlignment(QtCore.Qt.AlignCenter)
        self.image_label.setStyleSheet('background: #222; color: #eee; font-size: 16px; border: 2px dashed #666;')
        self.image_label.setMinimumHeight(400)

        # 串口选择下拉框
        self.port_combo = QtWidgets.QComboBox()
        self.port_combo.setMinimumWidth(120)
        self.refresh_ports()

        # 刷新串口按钮
        self.refresh_btn = QtWidgets.QPushButton('刷新串口')
        self.refresh_btn.clicked.connect(self.refresh_ports)

        # 新增：是否串口模式与波特率输入
        self.serial_checkbox = QtWidgets.QCheckBox('串口模式')
        self.serial_checkbox.setChecked(True)  # 默认启用串口模式
        self.baud_input = QtWidgets.QLineEdit('115200')
        self.baud_input.setMaximumWidth(100)
        self.baud_input.setToolTip('串口波特率，MaixCAM Pro 通常使用 115200')

        self.start_btn = QtWidgets.QPushButton('连接 MaixCAM Pro')
        self.start_btn.setStyleSheet('QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }')
        self.stop_btn = QtWidgets.QPushButton('断开连接')
        self.stop_btn.setStyleSheet('QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }')
        self.snap_btn = QtWidgets.QPushButton('保存截图')
        self.snap_btn.setStyleSheet('QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }')
        self.status_label = QtWidgets.QLabel('状态: 就绪 - 请连接 MaixCAM Pro 设备')

        # 连接设置区域
        connection_group = QtWidgets.QGroupBox('连接设置')
        connection_layout = QtWidgets.QHBoxLayout(connection_group)
        connection_layout.addWidget(QtWidgets.QLabel('串口:'))
        connection_layout.addWidget(self.port_combo)
        connection_layout.addWidget(self.refresh_btn)
        connection_layout.addWidget(self.serial_checkbox)
        connection_layout.addWidget(QtWidgets.QLabel('波特率:'))
        connection_layout.addWidget(self.baud_input)
        connection_layout.addStretch()

        # 控制按钮区域
        control_layout = QtWidgets.QHBoxLayout()
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.snap_btn)
        control_layout.addStretch()

        # 主布局
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.addWidget(connection_group)
        main_layout.addWidget(self.image_label, stretch=1)
        main_layout.addLayout(control_layout)
        main_layout.addWidget(self.status_label)

        self.grabber = None
        self.current_frame = None

        self.start_btn.clicked.connect(self.start_capture)
        self.stop_btn.clicked.connect(self.stop_capture)
        self.snap_btn.clicked.connect(self.snapshot)

    def refresh_ports(self):
        """刷新可用串口列表"""
        self.port_combo.clear()
        if serial is None:
            self.port_combo.addItem('请安装 pyserial')
            return

        try:
            ports = serial.tools.list_ports.comports()
            if not ports:
                self.port_combo.addItem('未找到串口设备')
            else:
                for port in ports:
                    # 显示端口名和描述
                    display_text = f"{port.device}"
                    if port.description and port.description != 'n/a':
                        display_text += f" - {port.description}"
                    self.port_combo.addItem(display_text, port.device)

                # 尝试自动选择 MaixCAM 相关的设备
                for i in range(self.port_combo.count()):
                    text = self.port_combo.itemText(i).lower()
                    if any(keyword in text for keyword in ['usb', 'serial', 'ch340', 'cp210', 'ftdi']):
                        self.port_combo.setCurrentIndex(i)
                        break
        except Exception as e:
            self.port_combo.addItem(f'扫描串口失败: {str(e)}')

    def start_capture(self):
        if self.grabber is not None and self.grabber.isRunning():
            self.status_label.setText('状态: MaixCAM Pro 已在运行中')
            return

        serial_mode = self.serial_checkbox.isChecked()

        if serial_mode:
            # 使用串口模式连接 MaixCAM Pro
            current_data = self.port_combo.currentData()
            if not current_data or current_data.startswith('请') or current_data.startswith('未') or current_data.startswith('扫描'):
                self.status_label.setText('状态: 请选择有效的串口设备')
                return
            source = current_data
        else:
            # 使用摄像头模式（备用）
            try:
                source = int(self.port_combo.currentText())
            except:
                source = 0

        # 解析波特率
        try:
            baud = int(self.baud_input.text().strip()) if serial_mode else 115200
        except Exception:
            baud = 115200

        self.grabber = FrameGrabber(source, serial_mode=serial_mode, baud=baud)
        self.grabber.frame_ready.connect(self.on_frame)
        self.grabber.error.connect(self.on_error)
        self.grabber.start()

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        mode_text = "串口" if serial_mode else "摄像头"
        self.status_label.setText(f'状态: 正在连接 MaixCAM Pro ({mode_text}: {source}, 波特率: {baud})...')
        self.image_label.setText('正在连接 MaixCAM Pro，等待数据流...')

    def stop_capture(self):
        if self.grabber is None:
            self.status_label.setText('状态: MaixCAM Pro 未运行')
            return
        self.grabber.stop()
        self.grabber = None

        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText('状态: 已断开 MaixCAM Pro 连接')
        self.image_label.setText('未连接 MaixCAM Pro')
        self.current_frame = None

    def on_error(self, msg):
        self.status_label.setText('错误: ' + msg)
        # 重置UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.image_label.setText(f'连接失败: {msg}')

    def on_frame(self, frame: np.ndarray):
        # keep last raw frame
        self.current_frame = frame
        # convert BGR -> RGB
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb.shape
        bytes_per_line = ch * w
        qt_image = QtGui.QImage(rgb.data, w, h, bytes_per_line, QtGui.QImage.Format_RGB888)

        # 保持宽高比缩放
        label_size = self.image_label.size()
        pix = QtGui.QPixmap.fromImage(qt_image).scaled(
            label_size.width(), label_size.height(),
            QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation
        )
        self.image_label.setPixmap(pix)
        self.status_label.setText(f'状态: MaixCAM Pro 运行中 - 分辨率: {w}x{h}')

    def snapshot(self):
        if self.current_frame is None:
            self.status_label.setText('状态: 无可保存的图像帧')
            return
        # ensure snapshots dir
        out_dir = os.path.join(os.path.dirname(__file__), 'snapshots')
        os.makedirs(out_dir, exist_ok=True)
        timestamp = QtCore.QDateTime.currentDateTime().toString("yyyyMMdd_HHmmss")
        fname = os.path.join(out_dir, f'maixcam_snap_{timestamp}.png')
        success = cv2.imwrite(fname, self.current_frame)
        if success:
            self.status_label.setText(f'截图已保存: {os.path.basename(fname)}')
        else:
            self.status_label.setText('截图保存失败')


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    w = MainWindow()
    w.show()
    sys.exit(app.exec_())
