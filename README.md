# MaixCAM Pro PyQt5 实时识别查看器

这是一个用于连接 MaixCAM Pro 相机模块并实时显示识别画面的 PyQt5 应用程序。

## 功能特性

- 🔌 **USB串口连接**: 通过USB线连接MaixCAM Pro相机模块
- 📺 **实时显示**: 实时显示部署的识别画面
- 📸 **截图功能**: 保存当前识别画面
- 🔍 **自动串口检测**: 自动扫描并识别可用串口设备
- ⚙️ **灵活配置**: 支持自定义波特率和连接参数

## 安装依赖

1. 确保已安装 Python 3.7+
2. 安装所需依赖包：

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install PyQt5 opencv-python numpy pyserial
```

## 使用方法

### 1. 连接硬件
- 使用USB数据线连接MaixCAM Pro到电脑
- 确保MaixCAM Pro已正确部署识别模型并运行

### 2. 启动应用
```bash
cd PY_QT_app
python main.py
```

### 3. 配置连接
1. 点击"刷新串口"按钮扫描可用串口
2. 从下拉列表中选择MaixCAM Pro对应的串口
3. 确认波特率设置（通常为115200）
4. 确保"串口模式"已勾选

### 4. 开始识别
1. 点击"连接 MaixCAM Pro"按钮
2. 等待连接建立，应用将显示实时识别画面
3. 使用"保存截图"按钮可保存当前画面

## 故障排除

### 常见问题

1. **找不到串口设备**
   - 检查USB连接是否正常
   - 确认MaixCAM Pro驱动已正确安装
   - 尝试重新插拔USB线

2. **连接失败**
   - 检查波特率设置是否正确
   - 确认MaixCAM Pro正在运行并输出图像数据
   - 检查串口是否被其他程序占用

3. **图像显示异常**
   - 确认MaixCAM Pro输出的是JPEG格式图像
   - 检查数据传输是否稳定
   - 尝试重新连接

### 技术细节

- **支持的图像格式**: JPEG
- **数据传输协议**: 串口通信
- **默认波特率**: 115200
- **图像解码**: OpenCV
- **界面框架**: PyQt5

## 文件结构

```
PY_QT_app/
├── main.py          # 主程序文件
├── snapshots/       # 截图保存目录（自动创建）
└── ...
requirements.txt     # 依赖包列表
README.md           # 使用说明
```

## 开发说明

### 核心组件

1. **FrameGrabber**: 负责从串口读取JPEG数据流并解码
2. **MainWindow**: 主界面，处理用户交互和图像显示
3. **串口管理**: 自动检测和管理串口连接

### 扩展功能

可以根据需要添加以下功能：
- 录制视频
- 图像处理和分析
- 网络流传输
- 多设备同时连接

## 许可证

本项目仅供学习和研究使用。
