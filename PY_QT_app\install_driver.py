#!/usr/bin/env python3
"""
MaixCAM Pro USB驱动自动安装脚本
"""

import subprocess
import sys
import os
import time
import tempfile

def check_admin():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重新运行脚本"""
    try:
        import ctypes
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, " ".join(sys.argv), None, 1
        )
    except Exception as e:
        print(f"无法获取管理员权限: {e}")

def check_device():
    """检查MaixCAM Pro设备是否存在"""
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'DeviceID like "%VID_359F&PID_2120%"', 'get', 'Name,DeviceID,Status'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and 'VID_359F' in result.stdout:
            print("✓ 检测到MaixCAM Pro设备")
            return True
        else:
            print("❌ 未检测到MaixCAM Pro设备")
            print("请确保设备已连接并开机")
            return False
    except Exception as e:
        print(f"设备检查失败: {e}")
        return False

def create_inf_file():
    """创建自定义INF驱动文件"""
    inf_content = """[Version]
Signature="$Windows NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=MaixCAM
DriverVer=01/01/2024,*******

[Manufacturer]
%MfgName%=DeviceList, NTx86, NTamd64

[DeviceList.NTx86]
%DESCRIPTION%=DriverInstall, USB\\VID_359F&PID_2120&MI_02

[DeviceList.NTamd64]  
%DESCRIPTION%=DriverInstall, USB\\VID_359F&PID_2120&MI_02

[DriverInstall]
Include=mdmcpq.inf
CopyFiles=FakeModemCopyFileSection
AddReg=DriverInstall.AddReg

[DriverInstall.AddReg]
HKR,,DevLoader,,*vcomm
HKR,,Enumerator,,serenum.vxd

[DriverInstall.Services]
AddService=usbser, 0x00000002, DriverService

[DriverService]
DisplayName=%SERVICE%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\\usbser.sys

[Strings]
MfgName="MaixCAM"
DESCRIPTION="MaixCAM Pro Serial Port"
SERVICE="MaixCAM Pro Serial Driver"
"""
    
    # 创建临时INF文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.inf', delete=False) as f:
        f.write(inf_content)
        return f.name

def install_driver_pnputil(inf_file):
    """使用pnputil安装驱动"""
    try:
        print("正在使用pnputil安装驱动...")
        result = subprocess.run([
            'pnputil', '/add-driver', inf_file, '/install'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ 驱动安装成功")
            return True
        else:
            print(f"⚠ pnputil安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"pnputil安装出错: {e}")
        return False

def force_driver_update():
    """强制更新设备驱动为串口驱动"""
    try:
        print("正在尝试强制更新设备驱动...")
        
        # 获取设备实例ID
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'DeviceID like "%VID_359F&PID_2120%"', 'get', 'DeviceID'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'VID_359F&PID_2120' in line:
                    device_id = line.strip()
                    print(f"找到设备ID: {device_id}")
                    
                    # 尝试禁用然后启用设备
                    print("正在重新枚举设备...")
                    subprocess.run(['pnputil', '/restart-device', f'"{device_id}"'], 
                                 capture_output=True, timeout=10)
                    break
        
        return True
    except Exception as e:
        print(f"强制更新失败: {e}")
        return False

def check_com_ports():
    """检查是否出现了COM端口"""
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_SerialPort', 'get', 'DeviceID,Description'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            com_ports = [line.strip() for line in lines if 'COM' in line and line.strip()]
            
            if com_ports:
                print("✓ 检测到COM端口:")
                for port in com_ports:
                    print(f"  {port}")
                return True
            else:
                print("❌ 未检测到COM端口")
                return False
    except Exception as e:
        print(f"COM端口检查失败: {e}")
        return False

def main():
    print("=" * 50)
    print("MaixCAM Pro USB驱动自动安装脚本")
    print("=" * 50)
    print()
    
    # 检查管理员权限
    if not check_admin():
        print("❌ 需要管理员权限")
        print("正在尝试获取管理员权限...")
        run_as_admin()
        return
    
    print("✓ 已获得管理员权限")
    print()
    
    # 检查设备
    if not check_device():
        input("按回车键退出...")
        return
    
    print()
    
    # 创建INF文件
    print("正在创建驱动文件...")
    inf_file = create_inf_file()
    
    try:
        # 尝试安装驱动
        success = install_driver_pnputil(inf_file)
        
        if not success:
            print("尝试其他方法...")
            force_driver_update()
        
        # 等待设备重新枚举
        print("等待设备重新枚举...")
        time.sleep(5)
        
        # 检查结果
        print()
        print("正在验证安装结果...")
        if check_com_ports():
            print()
            print("🎉 驱动安装成功！")
            print("现在可以在主程序中连接MaixCAM Pro了")
        else:
            print()
            print("⚠ 自动安装可能未成功")
            print("请尝试手动安装:")
            print("1. 打开设备管理器")
            print("2. 找到带感叹号的MaixCAM设备")
            print("3. 右键 → 更新驱动程序")
            print("4. 选择'浏览我的电脑以查找驱动程序'")
            print("5. 选择'让我从计算机上的可用驱动程序列表中选取'")
            print("6. 设备类型选择'端口(COM和LPT)'")
            print("7. 厂商选择'Microsoft'，型号选择'USB Serial Device'")
    
    finally:
        # 清理临时文件
        try:
            os.unlink(inf_file)
        except:
            pass
    
    print()
    print("=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
