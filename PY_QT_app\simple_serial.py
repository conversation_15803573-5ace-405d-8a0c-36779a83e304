#!/usr/bin/env python3
"""
简单的串口通信实现
使用Windows API直接访问串口，不依赖pyserial
"""

import ctypes
from ctypes import wintypes
import time
import threading

# Windows API常量
GENERIC_READ = 0x80000000
GENERIC_WRITE = 0x40000000
OPEN_EXISTING = 3
INVALID_HANDLE_VALUE = -1

# 串口配置常量
CBR_9600 = 9600
CBR_19200 = 19200
CBR_38400 = 38400
CBR_57600 = 57600
CBR_115200 = 115200

NOPARITY = 0
ONESTOPBIT = 0
EVENPARITY = 2
ODDPARITY = 1

class DCB(ctypes.Structure):
    _fields_ = [
        ('DCBlength', wintypes.DWORD),
        ('BaudRate', wintypes.DWORD),
        ('fBinary', wintypes.DWORD, 1),
        ('fParity', wintypes.DWORD, 1),
        ('fOutxCtsFlow', wintypes.DWORD, 1),
        ('fOutxDsrFlow', wintypes.DWORD, 1),
        ('fDtrControl', wintypes.DWORD, 2),
        ('fDsrSensitivity', wintypes.DWORD, 1),
        ('fTXContinueOnXoff', wintypes.DWORD, 1),
        ('fOutX', wintypes.DWORD, 1),
        ('fInX', wintypes.DWORD, 1),
        ('fErrorChar', wintypes.DWORD, 1),
        ('fNull', wintypes.DWORD, 1),
        ('fRtsControl', wintypes.DWORD, 2),
        ('fAbortOnError', wintypes.DWORD, 1),
        ('fDummy2', wintypes.DWORD, 17),
        ('wReserved', wintypes.WORD),
        ('XonLim', wintypes.WORD),
        ('XoffLim', wintypes.WORD),
        ('ByteSize', ctypes.c_ubyte),
        ('Parity', ctypes.c_ubyte),
        ('StopBits', ctypes.c_ubyte),
        ('XonChar', ctypes.c_char),
        ('XoffChar', ctypes.c_char),
        ('ErrorChar', ctypes.c_char),
        ('EofChar', ctypes.c_char),
        ('EvtChar', ctypes.c_char),
        ('wReserved1', wintypes.WORD),
    ]

class COMMTIMEOUTS(ctypes.Structure):
    _fields_ = [
        ('ReadIntervalTimeout', wintypes.DWORD),
        ('ReadTotalTimeoutMultiplier', wintypes.DWORD),
        ('ReadTotalTimeoutConstant', wintypes.DWORD),
        ('WriteTotalTimeoutMultiplier', wintypes.DWORD),
        ('WriteTotalTimeoutConstant', wintypes.DWORD),
    ]

class SimpleSerial:
    def __init__(self, port, baudrate=115200, timeout=1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.handle = None
        self.is_open = False
        
    def open(self):
        """打开串口"""
        try:
            # 打开串口设备
            self.handle = ctypes.windll.kernel32.CreateFileW(
                f"\\\\.\\{self.port}",
                GENERIC_READ | GENERIC_WRITE,
                0,
                None,
                OPEN_EXISTING,
                0,
                None
            )
            
            if self.handle == INVALID_HANDLE_VALUE:
                raise Exception(f"无法打开串口 {self.port}")
            
            # 配置串口参数
            dcb = DCB()
            dcb.DCBlength = ctypes.sizeof(DCB)
            
            if not ctypes.windll.kernel32.GetCommState(self.handle, ctypes.byref(dcb)):
                raise Exception("获取串口状态失败")
            
            dcb.BaudRate = self.baudrate
            dcb.ByteSize = 8
            dcb.Parity = NOPARITY
            dcb.StopBits = ONESTOPBIT
            dcb.fBinary = 1
            dcb.fParity = 0
            
            if not ctypes.windll.kernel32.SetCommState(self.handle, ctypes.byref(dcb)):
                raise Exception("设置串口参数失败")
            
            # 设置超时
            timeouts = COMMTIMEOUTS()
            timeouts.ReadIntervalTimeout = 50
            timeouts.ReadTotalTimeoutMultiplier = 10
            timeouts.ReadTotalTimeoutConstant = int(self.timeout * 1000)
            timeouts.WriteTotalTimeoutMultiplier = 10
            timeouts.WriteTotalTimeoutConstant = 1000
            
            if not ctypes.windll.kernel32.SetCommTimeouts(self.handle, ctypes.byref(timeouts)):
                raise Exception("设置串口超时失败")
            
            self.is_open = True
            return True
            
        except Exception as e:
            if self.handle and self.handle != INVALID_HANDLE_VALUE:
                ctypes.windll.kernel32.CloseHandle(self.handle)
            self.handle = None
            raise e
    
    def close(self):
        """关闭串口"""
        if self.handle and self.handle != INVALID_HANDLE_VALUE:
            ctypes.windll.kernel32.CloseHandle(self.handle)
            self.handle = None
            self.is_open = False
    
    def read(self, size=1):
        """读取数据"""
        if not self.is_open:
            return b''
        
        buffer = ctypes.create_string_buffer(size)
        bytes_read = wintypes.DWORD()
        
        if ctypes.windll.kernel32.ReadFile(
            self.handle,
            buffer,
            size,
            ctypes.byref(bytes_read),
            None
        ):
            return buffer.raw[:bytes_read.value]
        else:
            return b''
    
    def write(self, data):
        """写入数据"""
        if not self.is_open:
            return 0
        
        if isinstance(data, str):
            data = data.encode()
        
        bytes_written = wintypes.DWORD()
        
        if ctypes.windll.kernel32.WriteFile(
            self.handle,
            data,
            len(data),
            ctypes.byref(bytes_written),
            None
        ):
            return bytes_written.value
        else:
            return 0
    
    def in_waiting(self):
        """获取接收缓冲区中的字节数"""
        if not self.is_open:
            return 0
        
        # 简化实现，总是返回0
        # 实际应该使用ClearCommError API
        return 0

def list_ports():
    """列出可用的COM端口"""
    import subprocess
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_SerialPort', 'get', 'DeviceID'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            ports = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if line and line.startswith('COM'):
                    ports.append(line)
            return ports
        else:
            return []
    except:
        return []

# 兼容pyserial的接口
class Serial(SimpleSerial):
    def __init__(self, port=None, baudrate=9600, timeout=None):
        super().__init__(port, baudrate, timeout or 1)
        if port:
            self.open()

# 模拟serial.tools.list_ports
class ListPorts:
    class ComPort:
        def __init__(self, device, description=""):
            self.device = device
            self.description = description
            self.hwid = ""
    
    @staticmethod
    def comports():
        ports = list_ports()
        return [ListPorts.ComPort(port, f"串口设备 {port}") for port in ports]

# 创建模块级别的对象
tools = type('tools', (), {})()
tools.list_ports = ListPorts()
