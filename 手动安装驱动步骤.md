# MaixCAM Pro 手动驱动安装步骤

## 🎯 当前状态确认

✅ **设备已检测到**: `USB\VID_359F&PID_2120&MI_02`  
❌ **驱动有问题**: 显示为"CDC NCM Error"  
❌ **无COM端口**: 需要安装串口驱动  

## 📋 详细安装步骤

### 第一步：打开设备管理器

1. **方法1**: 按 `Win + X` 键，选择"设备管理器"
2. **方法2**: 按 `Win + R` 键，输入 `devmgmt.msc`，回车
3. **方法3**: 右键"此电脑" → "属性" → "设备管理器"

### 第二步：找到MaixCAM Pro设备

在设备管理器中查找以下位置之一：

#### 🔍 可能的位置1：其他设备
- 展开 **"其他设备"** 分类
- 查找带有 **黄色感叹号** 的未知设备
- 设备名称可能显示为"未知设备"或"MaixCAM"

#### 🔍 可能的位置2：网络适配器  
- 展开 **"网络适配器"** 分类
- 查找 **"CDC NCM"** 或类似名称的设备
- 这个设备可能有错误标识

#### 🔍 可能的位置3：通用串行总线控制器
- 展开 **"通用串行总线控制器"**
- 查找 **"USB Composite Device"** 
- 右键查看属性，确认硬件ID包含 `VID_359F&PID_2120`

### 第三步：更新驱动程序

找到设备后：

1. **右键点击** MaixCAM Pro设备
2. 选择 **"更新驱动程序"**
3. 选择 **"浏览我的电脑以查找驱动程序"**
4. 选择 **"让我从计算机上的可用驱动程序列表中选取"**

### 第四步：选择正确的驱动类型

在驱动程序选择界面：

1. **设备类型**: 选择 **"端口(COM和LPT)"**
2. 点击 **"下一步"**

### 第五步：选择驱动程序

在厂商和型号列表中：

1. **厂商**: 选择 **"Microsoft"**
2. **型号**: 选择 **"USB Serial Device"** 或 **"USB Serial Port"**
3. 点击 **"下一步"**

### 第六步：确认安装

1. 如果出现警告对话框，点击 **"是"** 继续安装
2. 等待驱动安装完成
3. 点击 **"关闭"**

## ✅ 验证安装成功

安装完成后，检查以下内容：

### 1. 设备管理器检查
- 展开 **"端口(COM和LPT)"** 分类
- 应该看到新的COM端口，例如：
  - `USB Serial Port (COM3)`
  - `MaixCAM Pro (COM4)`
  - 或类似名称

### 2. 运行检测工具
```bash
cd PY_QT_app
python windows_serial_test.py
```

### 3. 测试主程序
```bash
python main.py
```
- 点击"刷新串口"按钮
- 应该能在下拉列表中看到COM端口

## 🔧 如果安装失败

### 尝试其他驱动选项：

1. **CH340驱动**:
   - 厂商选择 **"WCH"** 或 **"CH340"**
   - 型号选择 **"USB-SERIAL CH340"**

2. **CP210x驱动**:
   - 厂商选择 **"Silicon Labs"**
   - 型号选择 **"CP210x USB to UART Bridge"**

3. **通用CDC驱动**:
   - 厂商选择 **"Microsoft"**
   - 型号选择 **"USB Serial Device"**

### 下载专用驱动：

如果内置驱动不行，下载以下驱动之一：

- **CH340驱动**: http://www.wch.cn/downloads/CH341SER_EXE.html
- **CP210x驱动**: https://www.silabs.com/developers/usb-to-uart-bridge-vcp-drivers

## 🆘 故障排除

### 问题1：找不到设备
- 重新插拔USB线
- 尝试不同的USB端口
- 确认MaixCAM Pro已开机

### 问题2：驱动安装失败
- 以管理员权限运行设备管理器
- 禁用驱动程序签名检查
- 重启电脑后重试

### 问题3：安装后仍无COM端口
- 重启电脑
- 重新插拔设备
- 尝试其他驱动选项

## 📞 获取帮助

如果仍有问题：

1. **截图发送**:
   - 设备管理器截图
   - 错误信息截图

2. **查看日志**:
   - 事件查看器 → Windows日志 → 系统
   - 查找USB相关错误

3. **联系支持**:
   - Sipeed官方论坛
   - MaixCAM Pro用户群

## 🎉 成功标志

驱动安装成功后：
- ✅ 设备管理器中出现COM端口
- ✅ 检测工具能找到串口
- ✅ 主程序能连接设备
- ✅ 可以接收数据

---

**重要**: 请按照步骤仔细操作，大多数情况下选择"Microsoft" + "USB Serial Device"就能解决问题！
