@echo off
echo ========================================
echo MaixCAM Pro USB驱动自动安装脚本
echo ========================================
echo.

echo 正在检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 已获得管理员权限
) else (
    echo ❌ 需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在检查MaixCAM Pro设备...

:: 检查设备是否存在
wmic path Win32_PnPEntity where "DeviceID like '%VID_359F&PID_2120%'" get Name,DeviceID,Status 2>nul | findstr "VID_359F" >nul
if %errorLevel% == 0 (
    echo ✓ 检测到MaixCAM Pro设备
) else (
    echo ❌ 未检测到MaixCAM Pro设备
    echo 请确保设备已连接并开机
    pause
    exit /b 1
)

echo.
echo 正在尝试安装通用USB串口驱动...

:: 方法1: 使用pnputil安装通用串口驱动
echo 尝试方法1: 强制安装通用串口驱动...

:: 创建临时INF文件
echo [Version] > %TEMP%\maixcam_serial.inf
echo Signature="$Windows NT$" >> %TEMP%\maixcam_serial.inf
echo Class=Ports >> %TEMP%\maixcam_serial.inf
echo ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318} >> %TEMP%\maixcam_serial.inf
echo Provider=MaixCAM >> %TEMP%\maixcam_serial.inf
echo DriverVer=01/01/2024,******* >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [Manufacturer] >> %TEMP%\maixcam_serial.inf
echo %%MfgName%%=DeviceList, NTx86, NTamd64 >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DeviceList.NTx86] >> %TEMP%\maixcam_serial.inf
echo %%DESCRIPTION%%=DriverInstall, USB\VID_359F^&PID_2120^&MI_02 >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DeviceList.NTamd64] >> %TEMP%\maixcam_serial.inf
echo %%DESCRIPTION%%=DriverInstall, USB\VID_359F^&PID_2120^&MI_02 >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DriverInstall] >> %TEMP%\maixcam_serial.inf
echo Include=mdmcpq.inf >> %TEMP%\maixcam_serial.inf
echo CopyFiles=FakeModemCopyFileSection >> %TEMP%\maixcam_serial.inf
echo AddReg=DriverInstall.AddReg >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DriverInstall.AddReg] >> %TEMP%\maixcam_serial.inf
echo HKR,,DevLoader,,*vcomm >> %TEMP%\maixcam_serial.inf
echo HKR,,Enumerator,,serenum.vxd >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DriverInstall.Services] >> %TEMP%\maixcam_serial.inf
echo AddService=usbser, 0x00000002, DriverService >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [DriverService] >> %TEMP%\maixcam_serial.inf
echo DisplayName=%%SERVICE%% >> %TEMP%\maixcam_serial.inf
echo ServiceType=1 >> %TEMP%\maixcam_serial.inf
echo StartType=3 >> %TEMP%\maixcam_serial.inf
echo ErrorControl=1 >> %TEMP%\maixcam_serial.inf
echo ServiceBinary=%%12%%\usbser.sys >> %TEMP%\maixcam_serial.inf
echo. >> %TEMP%\maixcam_serial.inf
echo [Strings] >> %TEMP%\maixcam_serial.inf
echo MfgName="MaixCAM" >> %TEMP%\maixcam_serial.inf
echo DESCRIPTION="MaixCAM Pro Serial Port" >> %TEMP%\maixcam_serial.inf
echo SERVICE="MaixCAM Pro Serial Driver" >> %TEMP%\maixcam_serial.inf

:: 安装驱动
echo 正在安装自定义驱动...
pnputil /add-driver %TEMP%\maixcam_serial.inf /install
if %errorLevel% == 0 (
    echo ✓ 驱动安装成功
) else (
    echo ⚠ 自定义驱动安装失败，尝试其他方法...
)

:: 方法2: 使用devcon强制更新驱动
echo.
echo 尝试方法2: 强制更新设备驱动...

:: 尝试强制设备使用usbser.sys驱动
for /f "tokens=1" %%i in ('wmic path Win32_PnPEntity where "DeviceID like '%%VID_359F&PID_2120%%'" get DeviceID /format:value ^| findstr "="') do (
    set DEVICE_ID=%%i
)

if defined DEVICE_ID (
    echo 找到设备ID: %DEVICE_ID%
    :: 这里需要devcon工具，如果没有则跳过
    where devcon >nul 2>&1
    if %errorLevel% == 0 (
        echo 使用devcon更新驱动...
        devcon update %TEMP%\maixcam_serial.inf "%DEVICE_ID%"
    ) else (
        echo devcon工具不可用，跳过此方法
    )
)

echo.
echo 正在验证驱动安装结果...

:: 等待设备重新枚举
timeout /t 3 /nobreak >nul

:: 检查是否出现COM端口
wmic path Win32_SerialPort get DeviceID,Description 2>nul | findstr "COM" >nul
if %errorLevel% == 0 (
    echo ✓ 检测到COM端口，驱动安装成功！
    echo.
    echo 可用的COM端口:
    wmic path Win32_SerialPort get DeviceID,Description
) else (
    echo ❌ 未检测到COM端口
    echo.
    echo 请尝试以下手动步骤:
    echo 1. 打开设备管理器
    echo 2. 找到带感叹号的MaixCAM设备
    echo 3. 右键 → 更新驱动程序
    echo 4. 选择"浏览我的电脑以查找驱动程序"
    echo 5. 选择"让我从计算机上的可用驱动程序列表中选取"
    echo 6. 设备类型选择"端口(COM和LPT)"
    echo 7. 厂商选择"Microsoft"，型号选择"USB Serial Device"
)

:: 清理临时文件
del %TEMP%\maixcam_serial.inf 2>nul

echo.
echo ========================================
echo 驱动安装脚本执行完成
echo ========================================
pause
