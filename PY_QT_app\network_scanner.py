#!/usr/bin/env python3
"""
MaixCAM Pro 网络连接扫描工具
扫描并检测MaixCAM Pro的网络连接
"""

import subprocess
import socket
import requests
import time
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

def get_network_interfaces():
    """获取网络接口信息"""
    try:
        result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            interfaces = []
            current_interface = None
            
            for line in result.stdout.split('\n'):
                line = line.strip()
                if '适配器' in line and ':' in line:
                    current_interface = line.split(':')[0].split('适配器')[-1].strip()
                elif 'IPv4 地址' in line and current_interface:
                    ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
                    if ip_match:
                        ip = ip_match.group(1)
                        interfaces.append((current_interface, ip))
                        current_interface = None
            
            return interfaces
        return []
    except:
        return []

def ping_host(ip):
    """ping指定IP地址"""
    try:
        result = subprocess.run(['ping', ip, '-n', '1', '-w', '1000'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def scan_maixcam_ports(ip):
    """扫描MaixCAM Pro常用端口"""
    common_ports = [22, 80, 8080, 8000, 5000, 18811, 18812, 18813]
    open_ports = []
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((ip, port))
            if result == 0:
                open_ports.append(port)
            sock.close()
        except:
            pass
    
    return open_ports

def test_http_endpoints(ip):
    """测试HTTP端点"""
    endpoints = [
        f"http://{ip}",
        f"http://{ip}:8080",
        f"http://{ip}:8000", 
        f"http://{ip}/capture",
        f"http://{ip}:8080/capture",
        f"http://{ip}/stream",
        f"http://{ip}:8080/stream"
    ]
    
    working_endpoints = []
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=2)
            if response.status_code == 200:
                working_endpoints.append((endpoint, response.status_code, len(response.content)))
        except:
            pass
    
    return working_endpoints

def scan_subnet(base_ip):
    """扫描子网寻找MaixCAM Pro设备"""
    base = '.'.join(base_ip.split('.')[:-1])
    potential_ips = []
    
    # 常见的MaixCAM Pro IP地址
    common_last_octets = [1, 100, 101, 102, 200, 201, 202]
    
    def check_ip(ip):
        if ping_host(ip):
            ports = scan_maixcam_ports(ip)
            if ports:
                return (ip, ports)
        return None
    
    # 使用线程池并行检查
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for octet in common_last_octets:
            ip = f"{base}.{octet}"
            futures.append(executor.submit(check_ip, ip))
        
        for future in as_completed(futures):
            result = future.result()
            if result:
                potential_ips.append(result)
    
    return potential_ips

def main():
    print("=" * 60)
    print("MaixCAM Pro 网络连接扫描工具")
    print("=" * 60)
    print()
    
    # 获取网络接口
    print("🔍 检查网络接口...")
    interfaces = get_network_interfaces()
    
    if interfaces:
        print("检测到的网络接口:")
        for name, ip in interfaces:
            print(f"  {name}: {ip}")
    else:
        print("未检测到网络接口")
    print()
    
    # 检查可能的MaixCAM Pro设备
    print("🔍 扫描MaixCAM Pro设备...")
    maixcam_devices = []
    
    # 检查常见的MaixCAM Pro IP地址
    common_ips = [
        "***********",
        "*************", 
        "*************",
        "********"
    ]
    
    for ip in common_ips:
        print(f"  检查 {ip}...")
        if ping_host(ip):
            print(f"    ✓ {ip} 可以ping通")
            ports = scan_maixcam_ports(ip)
            if ports:
                print(f"    ✓ 开放端口: {ports}")
                maixcam_devices.append((ip, ports))
            else:
                print(f"    ⚠ 未检测到常用端口")
        else:
            print(f"    ❌ {ip} 无法ping通")
    
    # 扫描本地网络
    print("\n🔍 扫描本地网络...")
    for name, ip in interfaces:
        if ip.startswith('10.') or ip.startswith('192.168.'):
            print(f"  扫描 {name} 网段...")
            subnet_devices = scan_subnet(ip)
            for device_ip, ports in subnet_devices:
                if device_ip not in [d[0] for d in maixcam_devices]:
                    maixcam_devices.append((device_ip, ports))
                    print(f"    ✓ 发现设备: {device_ip} 端口: {ports}")
    
    print()
    print("=" * 60)
    print("扫描结果")
    print("=" * 60)
    
    if maixcam_devices:
        print(f"✅ 发现 {len(maixcam_devices)} 个可能的MaixCAM Pro设备:")
        
        for i, (ip, ports) in enumerate(maixcam_devices, 1):
            print(f"\n设备 {i}: {ip}")
            print(f"  开放端口: {ports}")
            
            # 测试HTTP端点
            print("  测试HTTP端点...")
            endpoints = test_http_endpoints(ip)
            if endpoints:
                print("  ✓ 可用的HTTP端点:")
                for endpoint, status, size in endpoints:
                    print(f"    {endpoint} (状态: {status}, 大小: {size} 字节)")
            else:
                print("  ❌ 未找到可用的HTTP端点")
        
        print(f"\n💡 建议使用的连接方式:")
        for ip, ports in maixcam_devices:
            if 80 in ports:
                print(f"  HTTP: http://{ip}")
            if 8080 in ports:
                print(f"  HTTP: http://{ip}:8080")
            if 22 in ports:
                print(f"  SSH: ssh root@{ip}")
    else:
        print("❌ 未发现MaixCAM Pro设备")
        print("\n💡 请检查:")
        print("  1. MaixCAM Pro是否已连接并开机")
        print("  2. USB数据线是否正常")
        print("  3. 设备驱动是否已安装")
        print("  4. 是否在设备管理器中看到网络适配器")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
