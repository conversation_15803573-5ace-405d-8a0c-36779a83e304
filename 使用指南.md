# MaixCAM Pro 实时识别查看器 - 使用指南

## 🚀 快速开始

### 1. 环境准备
确保你的系统已安装：
- Python 3.7 或更高版本
- 必要的Python包（程序会自动检测并提示安装）

### 2. 硬件连接
1. 使用USB数据线连接MaixCAM Pro到电脑
2. 确保MaixCAM Pro已烧录识别程序并正常运行
3. 检查设备管理器中是否出现新的串口设备

### 3. 启动程序

#### Windows用户：
双击运行 `start_maixcam_viewer.bat`

#### Linux/Mac用户：
```bash
chmod +x start_maixcam_viewer.sh
./start_maixcam_viewer.sh
```

#### 手动启动：
```bash
cd PY_QT_app
python main.py
```

## 📋 操作步骤

### 第一步：检测串口
1. 启动程序后，界面会自动扫描可用串口
2. 如果没有检测到串口，点击"刷新串口"按钮
3. 从下拉列表中选择MaixCAM Pro对应的串口

### 第二步：配置连接参数
1. 确保"串口模式"已勾选 ✅
2. 检查波特率设置（通常为115200）
3. 如果不确定参数，可以先运行测试脚本：
   ```bash
   cd PY_QT_app
   python test_connection.py
   ```

### 第三步：建立连接
1. 点击"连接 MaixCAM Pro"按钮
2. 等待连接建立（状态栏会显示连接进度）
3. 成功连接后，界面将显示实时识别画面

### 第四步：使用功能
- **实时查看**：识别画面会自动更新显示
- **保存截图**：点击"保存截图"按钮保存当前画面
- **断开连接**：点击"断开连接"按钮停止数据流

## 🔧 故障排除

### 问题1：找不到串口设备
**可能原因：**
- USB连接不稳定
- 驱动程序未安装
- 设备未正确识别

**解决方案：**
1. 重新插拔USB线
2. 检查设备管理器中的串口设备
3. 安装MaixCAM Pro的USB驱动程序
4. 尝试使用不同的USB端口

### 问题2：连接失败
**可能原因：**
- 波特率设置错误
- 串口被其他程序占用
- MaixCAM Pro未运行识别程序

**解决方案：**
1. 确认波特率设置为115200
2. 关闭其他可能使用串口的程序
3. 重启MaixCAM Pro设备
4. 检查MaixCAM Pro是否正在运行识别程序

### 问题3：无图像显示
**可能原因：**
- MaixCAM Pro未输出图像数据
- 数据格式不正确
- 传输中断

**解决方案：**
1. 确认MaixCAM Pro正在运行识别程序
2. 检查MaixCAM Pro的输出格式是否为JPEG
3. 尝试重新连接
4. 运行测试脚本检查数据传输

### 问题4：图像显示异常
**可能原因：**
- 数据传输不稳定
- 图像解码错误

**解决方案：**
1. 检查USB连接质量
2. 尝试降低传输速率
3. 重新启动程序

## 📁 文件说明

```
QT/
├── PY_QT_app/
│   ├── main.py              # 主程序
│   ├── test_connection.py   # 连接测试脚本
│   └── snapshots/           # 截图保存目录
├── requirements.txt         # Python依赖包
├── README.md               # 项目说明
├── 使用指南.md             # 详细使用指南
├── start_maixcam_viewer.bat # Windows启动脚本
└── start_maixcam_viewer.sh  # Linux/Mac启动脚本
```

## 🔍 高级功能

### 测试连接
在连接MaixCAM Pro之前，建议先运行测试脚本：
```bash
cd PY_QT_app
python test_connection.py
```
这个脚本会：
- 扫描所有可用串口
- 测试串口连接
- 检查数据传输
- 验证JPEG图像数据

### 自定义配置
你可以根据需要修改以下参数：
- **波特率**：在界面中直接修改
- **超时时间**：修改main.py中的timeout参数
- **缓冲区大小**：修改main.py中的读取缓冲区大小

## 📞 技术支持

如果遇到问题：
1. 首先查看状态栏的错误信息
2. 运行测试脚本进行诊断
3. 检查MaixCAM Pro设备状态
4. 确认所有依赖包已正确安装

## 🎯 注意事项

1. **数据格式**：确保MaixCAM Pro输出JPEG格式的图像
2. **传输稳定性**：使用质量好的USB数据线
3. **资源占用**：长时间运行时注意内存使用情况
4. **兼容性**：支持Windows、Linux、macOS系统
