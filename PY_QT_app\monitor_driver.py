#!/usr/bin/env python3
"""
实时监控驱动安装进度
"""

import subprocess
import time
import os

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def check_maixcam_device():
    """检查MaixCAM设备状态"""
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'DeviceID like "%VID_359F&PID_2120%"', 'get', 'Name,DeviceID,Status'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            devices = []
            for line in lines[1:]:  # 跳过标题行
                line = line.strip()
                if line and 'VID_359F' in line:
                    devices.append(line)
            return devices
        return []
    except:
        return []

def check_com_ports():
    """检查COM端口"""
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_SerialPort', 'get', 'DeviceID,Description'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            ports = []
            for line in lines[1:]:  # 跳过标题行
                line = line.strip()
                if line and 'COM' in line:
                    ports.append(line)
            return ports
        return []
    except:
        return []

def check_error_devices():
    """检查有错误的设备"""
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'ConfigManagerErrorCode!=0', 'get', 'Name,DeviceID,ConfigManagerErrorCode'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            errors = []
            for line in lines[1:]:  # 跳过标题行
                line = line.strip()
                if line and ('VID_359F' in line or 'CDC' in line):
                    errors.append(line)
            return errors
        return []
    except:
        return []

def main():
    print("MaixCAM Pro 驱动安装监控工具")
    print("=" * 50)
    print("正在实时监控设备状态...")
    print("按 Ctrl+C 退出")
    print()
    
    last_status = None
    
    try:
        while True:
            clear_screen()
            print("MaixCAM Pro 驱动安装监控工具")
            print("=" * 50)
            print(f"监控时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 检查MaixCAM设备
            print("🔍 MaixCAM Pro 设备状态:")
            maixcam_devices = check_maixcam_device()
            if maixcam_devices:
                for device in maixcam_devices:
                    print(f"  ✓ {device}")
            else:
                print("  ❌ 未检测到MaixCAM Pro设备")
            print()
            
            # 检查COM端口
            print("🔗 COM端口状态:")
            com_ports = check_com_ports()
            if com_ports:
                print("  ✅ 检测到COM端口:")
                for port in com_ports:
                    print(f"    {port}")
                
                # 如果检测到COM端口，说明驱动安装成功
                if last_status != "success":
                    print()
                    print("🎉 驱动安装成功！")
                    print("现在可以关闭此窗口，在主程序中连接MaixCAM Pro")
                    last_status = "success"
            else:
                print("  ❌ 未检测到COM端口")
                last_status = "no_com"
            print()
            
            # 检查错误设备
            print("⚠️ 错误设备状态:")
            error_devices = check_error_devices()
            if error_devices:
                print("  检测到有问题的设备:")
                for error in error_devices:
                    print(f"    {error}")
                print("  💡 这些设备需要安装驱动程序")
            else:
                print("  ✓ 未检测到错误设备")
            print()
            
            # 显示当前状态
            if com_ports:
                print("📊 当前状态: ✅ 驱动已安装，可以连接")
            elif maixcam_devices and error_devices:
                print("📊 当前状态: ⚠️ 设备已连接，但需要安装驱动")
            elif maixcam_devices:
                print("📊 当前状态: 🔄 设备已连接，正在等待驱动安装")
            else:
                print("📊 当前状态: ❌ 未检测到MaixCAM Pro设备")
            
            print()
            print("💡 提示:")
            if not com_ports:
                print("  1. 请按照'手动安装驱动步骤.md'中的说明操作")
                print("  2. 在设备管理器中手动安装USB串口驱动")
                print("  3. 此工具会自动检测安装结果")
            else:
                print("  1. 驱动安装成功！")
                print("  2. 可以运行主程序: python main.py")
                print("  3. 在主程序中点击'刷新串口'按钮")
            
            print()
            print("按 Ctrl+C 退出监控")
            
            # 等待5秒后刷新
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n\n监控已停止")

if __name__ == "__main__":
    main()
