#!/usr/bin/env python3
"""
MaixCAM Pro 网络连接测试工具
测试各种可能的连接方式
"""

import requests
import socket
import subprocess
import time
import cv2
import numpy as np

def ping_host(ip):
    """ping测试"""
    try:
        result = subprocess.run(['ping', ip, '-n', '1', '-w', '1000'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def test_tcp_port(ip, port):
    """测试TCP端口"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def test_http_endpoint(url):
    """测试HTTP端点"""
    try:
        response = requests.get(url, timeout=3)
        return response.status_code, len(response.content), response.headers.get('content-type', '')
    except Exception as e:
        return None, 0, str(e)

def test_image_endpoint(url):
    """测试图像端点"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            # 尝试解码为图像
            arr = np.frombuffer(response.content, dtype=np.uint8)
            frame = cv2.imdecode(arr, cv2.IMREAD_COLOR)
            if frame is not None:
                return True, frame.shape, len(response.content)
            else:
                return False, None, len(response.content)
        return False, None, 0
    except Exception as e:
        return False, str(e), 0

def main():
    print("=" * 60)
    print("MaixCAM Pro 网络连接测试工具")
    print("=" * 60)
    print()
    
    # 测试目标IP
    target_ip = "***********"
    
    print(f"🔍 测试目标: {target_ip}")
    print()
    
    # 1. Ping测试
    print("1. Ping 连通性测试...")
    if ping_host(target_ip):
        print(f"   ✅ {target_ip} 可以ping通")
    else:
        print(f"   ❌ {target_ip} 无法ping通")
        print("   请检查MaixCAM Pro是否已连接并开机")
        return
    print()
    
    # 2. 端口扫描
    print("2. 端口扫描...")
    common_ports = [22, 80, 8080, 8000, 5000, 18811, 18812, 18813]
    open_ports = []
    
    for port in common_ports:
        if test_tcp_port(target_ip, port):
            open_ports.append(port)
            print(f"   ✅ 端口 {port} 开放")
        else:
            print(f"   ❌ 端口 {port} 关闭")
    
    if not open_ports:
        print("   ⚠️ 未发现开放的HTTP端口")
    print()
    
    # 3. HTTP服务测试
    print("3. HTTP服务测试...")
    base_urls = [
        f"http://{target_ip}",
        f"http://{target_ip}:8080",
        f"http://{target_ip}:8000"
    ]
    
    working_base = None
    for base_url in base_urls:
        status, size, content_type = test_http_endpoint(base_url)
        if status == 200:
            print(f"   ✅ {base_url} - 状态: {status}, 大小: {size} 字节")
            working_base = base_url
            break
        elif status:
            print(f"   ⚠️ {base_url} - 状态: {status}")
        else:
            print(f"   ❌ {base_url} - 连接失败")
    print()
    
    # 4. 图像端点测试
    print("4. 图像端点测试...")
    if working_base:
        image_endpoints = [
            f"{working_base}/capture",
            f"{working_base}/image",
            f"{working_base}/stream",
            f"{working_base}/mjpeg",
            f"{working_base}/camera",
            f"{working_base}/snapshot"
        ]
        
        working_image_endpoint = None
        for endpoint in image_endpoints:
            print(f"   测试 {endpoint}...")
            is_image, shape_or_error, size = test_image_endpoint(endpoint)
            if is_image:
                print(f"     ✅ 成功获取图像 - 尺寸: {shape_or_error}, 大小: {size} 字节")
                working_image_endpoint = endpoint
                break
            else:
                print(f"     ❌ 不是有效图像 - {shape_or_error}")
        
        if working_image_endpoint:
            print(f"\n   🎉 找到可用的图像端点: {working_image_endpoint}")
        else:
            print(f"\n   ⚠️ 未找到可用的图像端点")
    else:
        print("   ⚠️ 无可用的HTTP服务，跳过图像端点测试")
    print()
    
    # 5. SSH连接测试
    print("5. SSH连接测试...")
    if 22 in open_ports:
        print(f"   ✅ SSH端口开放，可以使用: ssh root@{target_ip}")
        print("   💡 默认密码通常为空或'root'")
    else:
        print("   ❌ SSH端口未开放")
    print()
    
    # 6. 总结和建议
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if working_base:
        print("✅ MaixCAM Pro 网络连接正常")
        print(f"📍 设备地址: {target_ip}")
        print(f"🌐 HTTP服务: {working_base}")
        
        if 'working_image_endpoint' in locals() and working_image_endpoint:
            print(f"📷 图像端点: {working_image_endpoint}")
            print("\n💡 建议在主程序中使用:")
            print(f"   连接方式: 网络连接")
            print(f"   地址: {target_ip}")
            print(f"   端口: {working_base.split(':')[-1] if ':' in working_base else '80'}")
        else:
            print("⚠️ 未找到图像端点，可能需要:")
            print("   1. 在MaixCAM Pro上启动图像服务")
            print("   2. 检查MaixCAM Pro的应用程序")
            print("   3. 查看MaixCAM Pro的文档")
        
        if 22 in open_ports:
            print(f"\n🔧 SSH访问: ssh root@{target_ip}")
    else:
        print("❌ MaixCAM Pro 网络连接异常")
        print("\n🔧 故障排除建议:")
        print("   1. 检查USB连接")
        print("   2. 重新插拔USB线")
        print("   3. 检查设备管理器中的网络适配器")
        print("   4. 重启MaixCAM Pro设备")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
