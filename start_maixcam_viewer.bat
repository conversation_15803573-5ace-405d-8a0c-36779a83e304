@echo off
echo MaixCAM Pro PyQt5 实时识别查看器
echo ================================

cd /d "%~dp0"

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt5, cv2, numpy, serial" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 依赖包安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo 启动MaixCAM Pro查看器...
cd PY_QT_app
python main.py

pause
