#!/bin/bash

echo "MaixCAM Pro PyQt5 实时识别查看器"
echo "================================"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "检查依赖包..."
python3 -c "import PyQt5, cv2, numpy, serial" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "依赖包安装失败，请手动运行: pip3 install -r requirements.txt"
        exit 1
    fi
fi

echo "启动MaixCAM Pro查看器..."
cd PY_QT_app
python3 main.py
