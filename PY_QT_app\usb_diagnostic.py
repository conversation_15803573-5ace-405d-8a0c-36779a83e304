#!/usr/bin/env python3
"""
MaixCAM Pro USB连接诊断工具
用于诊断和解决USB连接问题
"""

import sys
import os
import subprocess
import time
import platform

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False

def print_header():
    print("=" * 60)
    print("MaixCAM Pro USB连接诊断工具")
    print("=" * 60)
    print()

def check_system_info():
    """检查系统信息"""
    print("📋 系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {sys.version}")
    print(f"  pyserial可用: {'是' if SERIAL_AVAILABLE else '否'}")
    print()

def check_usb_devices():
    """检查USB设备"""
    print("🔌 USB设备检查:")
    
    system = platform.system()
    
    if system == "Windows":
        try:
            # 使用wmic命令查看USB设备
            result = subprocess.run(['wmic', 'path', 'Win32_PnPEntity', 'where', 
                                   'DeviceID like "%USB%"', 'get', 'Name,DeviceID'], 
                                   capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                usb_devices = [line.strip() for line in lines if line.strip() and 'USB' in line]
                
                if usb_devices:
                    print("  检测到的USB设备:")
                    for device in usb_devices[:10]:  # 只显示前10个
                        print(f"    {device}")
                else:
                    print("  ❌ 未检测到USB设备")
            else:
                print("  ⚠️ 无法获取USB设备信息")
        except Exception as e:
            print(f"  ❌ USB设备检查失败: {e}")
    
    elif system == "Linux":
        try:
            # 使用lsusb命令
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("  USB设备列表:")
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        print(f"    {line}")
            else:
                print("  ❌ lsusb命令执行失败")
        except FileNotFoundError:
            print("  ⚠️ lsusb命令不可用，请安装usbutils")
        except Exception as e:
            print(f"  ❌ USB设备检查失败: {e}")
    
    elif system == "Darwin":  # macOS
        try:
            result = subprocess.run(['system_profiler', 'SPUSBDataType'], 
                                   capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                print("  USB设备信息已获取（输出较长，仅显示关键信息）")
                # 简化输出
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Product ID' in line or 'Vendor ID' in line or 'Serial Number' in line:
                        print(f"    {line.strip()}")
            else:
                print("  ❌ system_profiler命令执行失败")
        except Exception as e:
            print(f"  ❌ USB设备检查失败: {e}")
    
    print()

def check_serial_ports():
    """检查串口设备"""
    print("🔗 串口设备检查:")
    
    if not SERIAL_AVAILABLE:
        print("  ❌ pyserial未安装，无法检查串口")
        print("  💡 请运行: pip install pyserial")
        print()
        return []
    
    try:
        ports = serial.tools.list_ports.comports()
        if not ports:
            print("  ❌ 未检测到任何串口设备")
            print()
            return []
        
        print(f"  ✅ 检测到 {len(ports)} 个串口设备:")
        for i, port in enumerate(ports):
            print(f"    {i+1}. {port.device}")
            print(f"       描述: {port.description}")
            print(f"       硬件ID: {port.hwid}")
            if hasattr(port, 'vid') and port.vid:
                print(f"       厂商ID: 0x{port.vid:04X}")
            if hasattr(port, 'pid') and port.pid:
                print(f"       产品ID: 0x{port.pid:04X}")
            print()
        
        return ports
    except Exception as e:
        print(f"  ❌ 串口检查失败: {e}")
        print()
        return []

def suggest_solutions():
    """提供解决方案建议"""
    print("💡 解决方案建议:")
    print()
    
    print("1. 🔧 驱动程序问题:")
    print("   - 确保MaixCAM Pro的USB驱动已正确安装")
    print("   - 常见驱动芯片: CH340, CP2102, FT232")
    print("   - Windows: 检查设备管理器中是否有未知设备或感叹号")
    print("   - Linux: 检查是否需要添加用户到dialout组")
    print()
    
    print("2. 🔌 硬件连接问题:")
    print("   - 确保使用数据线而非充电线")
    print("   - 尝试不同的USB端口")
    print("   - 检查USB线是否损坏")
    print("   - 确保MaixCAM Pro已开机并运行")
    print()
    
    print("3. ⚙️ 设备配置问题:")
    print("   - 检查MaixCAM Pro是否处于正确的USB模式")
    print("   - 某些设备需要特定的启动序列")
    print("   - 查看MaixCAM Pro的用户手册")
    print()
    
    print("4. 🖥️ 系统权限问题:")
    if platform.system() == "Linux":
        print("   - Linux用户需要串口访问权限:")
        print("     sudo usermod -a -G dialout $USER")
        print("     然后重新登录")
    elif platform.system() == "Windows":
        print("   - 以管理员权限运行程序")
        print("   - 检查Windows安全软件是否阻止设备")
    print()

def download_drivers():
    """提供驱动下载链接"""
    print("📥 常用USB转串口驱动下载:")
    print()
    print("CH340/CH341 驱动:")
    print("  官方: http://www.wch.cn/downloads/CH341SER_EXE.html")
    print()
    print("CP210x 驱动:")
    print("  官方: https://www.silabs.com/developers/usb-to-uart-bridge-vcp-drivers")
    print()
    print("FTDI 驱动:")
    print("  官方: https://ftdichip.com/drivers/vcp-drivers/")
    print()

def test_specific_port(port_device):
    """测试特定串口"""
    if not SERIAL_AVAILABLE:
        print("❌ pyserial未安装，无法测试串口")
        return False
    
    print(f"🧪 测试串口: {port_device}")
    
    try:
        # 尝试不同的波特率
        baud_rates = [115200, 9600, 57600, 38400, 19200]
        
        for baud in baud_rates:
            print(f"  尝试波特率: {baud}")
            try:
                ser = serial.Serial(port_device, baud, timeout=2)
                print(f"    ✅ 连接成功")
                
                # 尝试读取数据
                time.sleep(1)
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting)
                    print(f"    📥 接收到 {len(data)} 字节数据")
                    if b'\xff\xd8' in data:
                        print("    🖼️ 检测到JPEG图像数据")
                else:
                    print("    ⚠️ 未接收到数据")
                
                ser.close()
                return True
                
            except Exception as e:
                print(f"    ❌ 连接失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    print_header()
    
    # 系统信息检查
    check_system_info()
    
    # USB设备检查
    check_usb_devices()
    
    # 串口设备检查
    ports = check_serial_ports()
    
    # 如果找到串口，询问是否测试
    if ports:
        print("🔍 是否要测试串口连接？")
        choice = input("输入 y 进行测试，其他键跳过: ").strip().lower()
        if choice == 'y':
            print()
            for i, port in enumerate(ports):
                print(f"{i+1}. {port.device} - {port.description}")
            
            try:
                selection = input(f"选择要测试的串口 (1-{len(ports)}): ").strip()
                port_index = int(selection) - 1
                if 0 <= port_index < len(ports):
                    test_specific_port(ports[port_index].device)
                else:
                    print("无效选择")
            except ValueError:
                print("输入无效")
            print()
    
    # 提供解决方案
    suggest_solutions()
    
    # 驱动下载链接
    download_drivers()
    
    print("=" * 60)
    print("诊断完成！")
    print("如果问题仍未解决，请:")
    print("1. 检查MaixCAM Pro的用户手册")
    print("2. 联系设备厂商技术支持")
    print("3. 在相关论坛寻求帮助")
    print("=" * 60)

if __name__ == "__main__":
    main()
