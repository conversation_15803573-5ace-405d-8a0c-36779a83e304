import argparse
import serial
import serial.tools.list_ports
import time
import sys
import traceback

def list_ports():
    """返回可用串口列表（如 ['COM3', 'COM4'] 或 ['/dev/ttyUSB0']）。"""
    return [p.device for p in serial.tools.list_ports.comports()]


def open_serial(port, baudrate, timeout=1):
    try:
        ser = serial.Serial(port, baudrate, timeout=timeout)
        print(f"已连接 {port}, 波特率 {baudrate}")
        return ser
    except FileNotFoundError as e:
        print(f"指定端口 {port} 不存在: {e}")
        return None
    except Exception as e:
        print("串口打开失败:", e)
        return None


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", "-p", help="串口号, 例如 COM3 或 /dev/ttyUSB0")
    parser.add_argument("--baud", "-b", type=int, default=115200)
    args = parser.parse_args()

    available = list_ports()

    ser = None

    if args.port:
        ser = open_serial(args.port, args.baud)
        if ser is None:
            if available:
                print("尝试使用第一个可用端口:", available[0])
                ser = open_serial(available[0], args.baud)
            else:
                print("没有可用的串口。请检查连接或使用 --port 指定正确的端口。")
                return
    else:
        if not available:
            print("未检测到可用串口。请插入设备或使用 --port 指定端口。")
            return
        print("检测到以下串口:", ", ".join(available))
        ser = open_serial(available[0], args.baud)
        if ser is None:
            print("无法打开任何串口。退出。")
            return

    try:
        while True:
            if ser.in_waiting:
                data = ser.readline().decode(errors="ignore").strip()
                if data:
                    print("接收到:", data)
                    if "label" in data:
                        try:
                            parts = [p.strip() for p in data.split(",")]
                            label = parts[0].split(":")[1].strip()
                            conf = float(parts[1].split(":")[1])
                            x = int(parts[2].split(":")[1])
                            y = int(parts[3].split(":")[1])
                            w = int(parts[4].split(":")[1])
                            h = int(parts[5].split(":")[1])
                            print(f"识别结果 -> 类别:{label}, 置信度:{conf:.2f}, 位置:({x},{y},{w},{h})")
                        except Exception:
                            print("数据解析失败:", data)
            else:
                time.sleep(0.01)

    except KeyboardInterrupt:
        print("用户中断")
    except Exception:
        print("运行时出现异常:")
        traceback.print_exc()
    finally:
        if 'ser' in locals() and ser and getattr(ser, 'is_open', False):
            ser.close()
            print("串口已关闭")


if __name__ == "__main__":
    main()

