#!/usr/bin/env python3
"""
MaixCAM Pro 连接测试脚本
用于测试串口连接和数据接收
"""

import sys
import time
try:
    import serial
    import serial.tools.list_ports
except ImportError:
    print("错误: 请安装 pyserial 库")
    print("运行: pip install pyserial")
    sys.exit(1)

def list_serial_ports():
    """列出所有可用的串口"""
    print("扫描可用串口...")
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("未找到任何串口设备")
        return []
    
    print(f"找到 {len(ports)} 个串口设备:")
    for i, port in enumerate(ports):
        print(f"  {i+1}. {port.device} - {port.description}")
    
    return ports

def test_serial_connection(port_name, baud_rate=115200):
    """测试串口连接"""
    print(f"\n正在测试串口连接: {port_name} (波特率: {baud_rate})")
    
    try:
        ser = serial.Serial(port_name, baud_rate, timeout=2)
        print("✓ 串口连接成功")
        
        # 尝试读取数据
        print("等待数据...")
        start_time = time.time()
        data_received = False
        
        while time.time() - start_time < 10:  # 等待10秒
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                if data:
                    data_received = True
                    print(f"✓ 接收到 {len(data)} 字节数据")
                    
                    # 检查是否包含JPEG标识
                    if b'\xff\xd8' in data:
                        print("✓ 检测到JPEG图像数据")
                    break
            time.sleep(0.1)
        
        if not data_received:
            print("⚠ 10秒内未接收到数据")
            print("  请确认MaixCAM Pro正在运行并输出图像数据")
        
        ser.close()
        return True
        
    except serial.SerialException as e:
        print(f"✗ 串口连接失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        return False

def main():
    print("MaixCAM Pro 连接测试工具")
    print("=" * 40)
    
    # 列出串口
    ports = list_serial_ports()
    if not ports:
        return
    
    # 让用户选择串口
    while True:
        try:
            choice = input(f"\n请选择要测试的串口 (1-{len(ports)}, 或按 q 退出): ").strip()
            if choice.lower() == 'q':
                return
            
            port_index = int(choice) - 1
            if 0 <= port_index < len(ports):
                selected_port = ports[port_index]
                break
            else:
                print("无效选择，请重新输入")
        except ValueError:
            print("请输入有效数字")
    
    # 测试连接
    success = test_serial_connection(selected_port.device)
    
    if success:
        print(f"\n✓ 串口 {selected_port.device} 测试完成")
        print("现在可以在主程序中使用此串口连接MaixCAM Pro")
    else:
        print(f"\n✗ 串口 {selected_port.device} 测试失败")
        print("请检查:")
        print("  1. MaixCAM Pro是否正确连接")
        print("  2. 设备驱动是否已安装")
        print("  3. MaixCAM Pro是否正在运行识别程序")

if __name__ == "__main__":
    main()
