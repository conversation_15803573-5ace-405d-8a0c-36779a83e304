#!/usr/bin/env python3
"""
Windows原生串口测试工具
不依赖pyserial，使用Windows API直接访问串口
"""

import sys
import time
import subprocess
import re

def get_com_ports():
    """获取Windows系统中的COM端口"""
    try:
        # 使用wmic命令获取串口信息
        result = subprocess.run([
            'wmic', 'path', 'Win32_SerialPort', 'get', 
            'DeviceID,Description,Name'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("❌ 无法获取串口信息")
            return []
        
        ports = []
        lines = result.stdout.strip().split('\n')
        
        for line in lines[1:]:  # 跳过标题行
            line = line.strip()
            if line and 'COM' in line:
                # 解析COM端口信息
                parts = [p.strip() for p in line.split() if p.strip()]
                if parts:
                    com_match = re.search(r'COM\d+', line)
                    if com_match:
                        com_port = com_match.group()
                        description = line.replace(com_port, '').strip()
                        ports.append((com_port, description))
        
        return ports
        
    except Exception as e:
        print(f"❌ 获取串口列表失败: {e}")
        return []

def test_com_port_with_mode(port):
    """使用mode命令测试COM端口"""
    try:
        # 使用mode命令配置串口
        result = subprocess.run([
            'mode', port, 'baud=115200', 'parity=n', 'data=8', 'stop=1'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print(f"✅ {port} 配置成功")
            return True
        else:
            print(f"❌ {port} 配置失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {port} 测试失败: {e}")
        return False

def check_device_manager():
    """检查设备管理器中的设备"""
    print("🔍 检查设备管理器中的串口设备...")
    
    try:
        # 获取所有PnP设备
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'Name like "%COM%" OR Name like "%串口%" OR Name like "%Serial%"',
            'get', 'Name,DeviceID,Status'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            devices = [line.strip() for line in lines if line.strip() and ('COM' in line or '串口' in line)]
            
            if devices:
                print("  检测到的串口相关设备:")
                for device in devices:
                    print(f"    {device}")
            else:
                print("  ❌ 未检测到串口设备")
        else:
            print("  ⚠️ 无法查询设备信息")
            
    except Exception as e:
        print(f"  ❌ 设备检查失败: {e}")

def check_unknown_devices():
    """检查未知设备"""
    print("\n🔍 检查未知设备...")
    
    try:
        result = subprocess.run([
            'wmic', 'path', 'Win32_PnPEntity', 'where', 
            'Status="Error" OR ConfigManagerErrorCode!=0',
            'get', 'Name,DeviceID,Status,ConfigManagerErrorCode'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            error_devices = [line.strip() for line in lines if line.strip() and line != lines[0]]
            
            if error_devices:
                print("  检测到有问题的设备:")
                for device in error_devices:
                    print(f"    {device}")
                print("\n  💡 这些设备可能需要安装驱动程序")
            else:
                print("  ✅ 未检测到有问题的设备")
        else:
            print("  ⚠️ 无法查询设备状态")
            
    except Exception as e:
        print(f"  ❌ 未知设备检查失败: {e}")

def open_device_manager():
    """打开设备管理器"""
    try:
        print("\n🔧 正在打开设备管理器...")
        subprocess.run(['devmgmt.msc'], timeout=5)
        print("✅ 设备管理器已打开")
        print("💡 请在设备管理器中查看:")
        print("   1. 端口(COM和LPT) - 查看是否有新的COM端口")
        print("   2. 其他设备 - 查看是否有未知设备")
        print("   3. 通用串行总线控制器 - 查看USB设备")
    except Exception as e:
        print(f"❌ 无法打开设备管理器: {e}")

def main():
    print("=" * 60)
    print("Windows原生串口检测工具")
    print("=" * 60)
    print()
    
    # 检查COM端口
    print("🔗 检查COM端口...")
    ports = get_com_ports()
    
    if ports:
        print(f"✅ 检测到 {len(ports)} 个COM端口:")
        for port, desc in ports:
            print(f"  {port}: {desc}")
        
        print("\n🧪 测试COM端口配置...")
        for port, desc in ports:
            test_com_port_with_mode(port)
    else:
        print("❌ 未检测到任何COM端口")
        print("💡 这表明MaixCAM Pro可能未被正确识别为串口设备")
    
    print()
    
    # 检查设备管理器
    check_device_manager()
    
    # 检查未知设备
    check_unknown_devices()
    
    print("\n" + "=" * 60)
    print("检测完成！")
    print("=" * 60)
    
    if not ports:
        print("\n🚨 未检测到COM端口，建议:")
        print("1. 安装MaixCAM Pro的USB驱动程序")
        print("2. 检查USB连接")
        print("3. 查看设备管理器中的未知设备")
        
        choice = input("\n是否打开设备管理器查看详细信息？(y/n): ").strip().lower()
        if choice == 'y':
            open_device_manager()
    else:
        print(f"\n✅ 检测到COM端口，可以尝试连接MaixCAM Pro")
        print("💡 建议使用以下端口进行测试:")
        for port, desc in ports:
            print(f"   {port} - {desc}")

if __name__ == "__main__":
    main()
